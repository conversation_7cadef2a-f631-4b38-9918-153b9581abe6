# Context
Filename: Task_MusicApp_Implementation.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
开发一个 Android 音乐 APP demo 项目，使用最新的 Jetpack Compose 和 MVI 架构。需要实现以下功能：

## UI 元素需求
1. **主页**：
   - 迷你播放器：主页底部有一个迷你播放器（包含专辑封面、歌曲名、播放/暂停按钮）
   - 底部导航栏：迷你播放器下方有一个底部导航栏，包含 5 个 tab（"首页"、"直播"、"搜索"、"社区"、"我的"）

2. **设置页**：
   - 迷你播放器：设置页底部也有相同的迷你播放器（但底部导航栏在设置页不可见）

## 导航和动画需求
1. **导航方式**：通过底部导航栏切换页面
2. **动画效果**：
   - 主页切换到设置页：迷你播放器和底部导航栏一起向下平滑移动，导航栏完全隐藏，设置页主体内容淡入显示
   - 设置页切换到主页：迷你播放器和底部导航栏一起向上平滑移动，导航栏完全显示，主页主体内容淡入显示
   - 动画时长：300 毫秒，缓动效果为 ease-in-out

## 技术要求
1. **UI 框架**：使用 Jetpack Compose 实现，APP 应使用 Scaffold 布局，BottomNavigation 实现底部导航栏
2. **架构**：使用 MVI 架构，定义 State 管理迷你播放器和导航栏显示状态，Intents 处理导航动作，SideEffects 触发动画
3. **边缘情况**：
   - 如果没有音乐播放，迷你播放器应隐藏（但导航栏仍可见）
   - UI 需适配不同屏幕尺寸

# Project Overview
这是一个基于 Jetpack Compose 的 Android 音乐播放器 demo 项目，当前项目结构：
- 包名：com.example.animebottomqq
- 使用 Kotlin 和 Jetpack Compose
- 目标 SDK：35，最低 SDK：24
- 当前只有基础的 MainActivity 和默认主题文件

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)
## 当前项目状态分析
1. **项目结构**：
   - 标准 Android 项目结构，使用 Gradle Kotlin DSL
   - 已配置 Jetpack Compose 支持
   - 包名：com.example.animebottomqq
   - 目标 SDK 35，最低 SDK 24

2. **现有依赖**：
   - Jetpack Compose BOM 2024.09.00
   - Material3 支持
   - Activity Compose
   - 基础的测试依赖

3. **当前代码状态**：
   - MainActivity.kt：只有基础的 "Hello Android" 示例
   - 主题文件：Color.kt, Theme.kt, Type.kt（标准主题文件）
   - 没有导航、MVI 架构或音乐播放相关代码

4. **需要添加的依赖**：
   - Navigation Compose（用于页面导航）
   - ViewModel 和 Lifecycle（用于 MVI 架构）
   - 可能需要的图标和动画库

5. **关键技术约束**：
   - 必须使用 MVI 架构模式
   - 需要实现复杂的动画效果（迷你播放器和导航栏同步移动）
   - 需要状态管理来控制迷你播放器的显示/隐藏
   - 需要适配不同屏幕尺寸

6. **核心文件需要创建**：
   - MVI 相关：State, Intent, ViewModel
   - UI 组件：MiniPlayer, BottomNavigation, 主页面, 设置页面
   - 导航设置：NavHost 配置
   - 动画实现：自定义动画组合
