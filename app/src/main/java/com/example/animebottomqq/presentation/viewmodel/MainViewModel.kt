package com.example.animebottomqq.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.animebottomqq.data.models.MiniPlayerState
import com.example.animebottomqq.data.models.PlaybackState
import com.example.animebottomqq.data.models.PlayerState
import com.example.animebottomqq.data.models.Song
import com.example.animebottomqq.presentation.intent.MainIntent
import com.example.animebottomqq.presentation.state.MainState
import com.example.animebottomqq.presentation.state.NavigationDestination
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 主界面 ViewModel - MVI 架构实现
 */
class MainViewModel : ViewModel() {
    
    private val _state = MutableStateFlow(MainState())
    val state: StateFlow<MainState> = _state.asStateFlow()
    
    /**
     * 处理用户意图
     */
    fun handleIntent(intent: MainIntent) {
        when (intent) {
            // 导航相关意图处理
            is MainIntent.NavigateTo -> navigateTo(intent.destination)
            is MainIntent.NavigateBack -> navigateBack()
            
            // 播放器控制意图处理
            is MainIntent.TogglePlayPause -> togglePlayPause()
            is MainIntent.PlaySong -> playSong(intent.song)
            is MainIntent.StopPlayback -> stopPlayback()
            is MainIntent.SeekTo -> seekTo(intent.position)
            
            // 迷你播放器显示控制
            is MainIntent.ShowMiniPlayer -> showMiniPlayer()
            is MainIntent.HideMiniPlayer -> hideMiniPlayer()
            
            // 动画控制
            is MainIntent.StartAnimation -> startAnimation()
            is MainIntent.EndAnimation -> endAnimation()
            is MainIntent.UpdateAnimationProgress -> updateAnimationProgress(intent.progress)
            
            // 错误处理
            is MainIntent.ShowError -> showError(intent.message)
            is MainIntent.ClearError -> clearError()
            
            // 加载状态
            is MainIntent.StartLoading -> startLoading()
            is MainIntent.StopLoading -> stopLoading()
        }
    }
    
    /**
     * 导航到指定目的地
     */
    private fun navigateTo(destination: NavigationDestination) {
        viewModelScope.launch {
            // 检查是否需要特殊动画处理
            val currentDest = _state.value.currentDestination
            val needsSpecialAnimation = (currentDest == NavigationDestination.Home && destination == NavigationDestination.Settings) ||
                                      (currentDest == NavigationDestination.Settings && destination == NavigationDestination.Home)

            if (needsSpecialAnimation) {
                // 开始动画状态
                _state.value = _state.value.copy(isAnimating = true)

                // 立即更新目的地，让动画组件处理位移动画
                _state.value = _state.value.copy(
                    currentDestination = destination,
                    isAnimating = true
                )

                // 等待动画完成（Spring 动画会自动处理时长）
                delay(400) // 稍微长一点确保 Spring 动画完成

                // 结束动画状态
                _state.value = _state.value.copy(isAnimating = false)
            } else {
                // 直接导航，无需特殊动画
                _state.value = _state.value.copy(currentDestination = destination)
            }
        }
    }
    
    /**
     * 返回导航
     */
    private fun navigateBack() {
        val currentDest = _state.value.currentDestination
        if (currentDest == NavigationDestination.Settings) {
            navigateTo(NavigationDestination.Profile) // 从设置页返回到"我的"页面
        }
    }
    
    /**
     * 切换播放/暂停状态
     */
    private fun togglePlayPause() {
        val currentPlayerState = _state.value.playerState
        val newPlaybackState = when (currentPlayerState.playbackState) {
            PlaybackState.Playing -> PlaybackState.Paused
            PlaybackState.Paused -> PlaybackState.Playing
            PlaybackState.Stopped -> PlaybackState.Playing
        }
        
        _state.value = _state.value.copy(
            playerState = currentPlayerState.copy(playbackState = newPlaybackState)
        )
    }
    
    /**
     * 播放指定歌曲
     */
    private fun playSong(song: Song) {
        _state.value = _state.value.copy(
            playerState = PlayerState(
                currentSong = song,
                playbackState = PlaybackState.Playing,
                miniPlayerState = MiniPlayerState.Visible
            )
        )
    }
    
    /**
     * 停止播放
     */
    private fun stopPlayback() {
        _state.value = _state.value.copy(
            playerState = PlayerState(
                currentSong = null,
                playbackState = PlaybackState.Stopped,
                miniPlayerState = MiniPlayerState.Hidden
            )
        )
    }
    
    /**
     * 跳转到指定位置
     */
    private fun seekTo(position: Long) {
        val currentPlayerState = _state.value.playerState
        _state.value = _state.value.copy(
            playerState = currentPlayerState.copy(currentPosition = position)
        )
    }
    
    /**
     * 显示迷你播放器
     */
    private fun showMiniPlayer() {
        val currentPlayerState = _state.value.playerState
        _state.value = _state.value.copy(
            playerState = currentPlayerState.copy(miniPlayerState = MiniPlayerState.Visible)
        )
    }
    
    /**
     * 隐藏迷你播放器
     */
    private fun hideMiniPlayer() {
        val currentPlayerState = _state.value.playerState
        _state.value = _state.value.copy(
            playerState = currentPlayerState.copy(miniPlayerState = MiniPlayerState.Hidden)
        )
    }
    
    /**
     * 开始动画
     */
    private fun startAnimation() {
        _state.value = _state.value.copy(isAnimating = true)
    }
    
    /**
     * 结束动画
     */
    private fun endAnimation() {
        _state.value = _state.value.copy(isAnimating = false, animationProgress = 0f)
    }
    
    /**
     * 更新动画进度
     */
    private fun updateAnimationProgress(progress: Float) {
        _state.value = _state.value.copy(animationProgress = progress)
    }
    
    /**
     * 显示错误信息
     */
    private fun showError(message: String) {
        _state.value = _state.value.copy(errorMessage = message)
    }
    
    /**
     * 清除错误信息
     */
    private fun clearError() {
        _state.value = _state.value.copy(errorMessage = null)
    }
    
    /**
     * 开始加载
     */
    private fun startLoading() {
        _state.value = _state.value.copy(isLoading = true)
    }
    
    /**
     * 停止加载
     */
    private fun stopLoading() {
        _state.value = _state.value.copy(isLoading = false)
    }
    
    /**
     * 初始化示例数据（用于演示）
     */
    fun initializeSampleData() {
        val sampleSong = Song(
            id = "sample_1",
            title = "把回忆拼好给你 (Live)",
            artist = "Miu",
            albumCover = null,
            duration = 240000L // 4分钟
        )
        
        playSong(sampleSong)
    }
}
