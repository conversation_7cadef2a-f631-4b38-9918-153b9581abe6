package com.example.animebottomqq.presentation.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.rememberNavController
import com.example.animebottomqq.navigation.AppNavigation
import com.example.animebottomqq.navigation.NavigationDestinations
import com.example.animebottomqq.navigation.toRoute
import com.example.animebottomqq.presentation.components.AnimatedBottomSection
import com.example.animebottomqq.presentation.intent.MainIntent
import com.example.animebottomqq.presentation.state.NavigationDestination
import com.example.animebottomqq.presentation.viewmodel.MainViewModel

/**
 * 主屏幕容器 - 整合所有组件和动画效果
 */
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val navController = rememberNavController()
    val configuration = LocalConfiguration.current
    
    // 监听导航状态变化
    LaunchedEffect(state.currentDestination) {
        val currentRoute = navController.currentDestination?.route
        val targetRoute = state.currentDestination.toRoute()
        
        if (currentRoute != targetRoute) {
            navController.navigate(targetRoute) {
                // 避免重复导航到同一页面
                launchSingleTop = true
                // 如果是从设置页返回，清除设置页的回退栈
                if (state.currentDestination != NavigationDestination.Settings) {
                    popUpTo(NavigationDestinations.SETTINGS) {
                        inclusive = true
                    }
                }
            }
        }
    }
    
    // 主布局使用 Scaffold
    Scaffold(
        modifier = modifier.fillMaxSize(),
        bottomBar = {
            // 动画底部区域（迷你播放器 + 导航栏）
            AnimatedBottomSection(
                state = state,
                onPlayPauseClick = {
                    viewModel.handleIntent(MainIntent.TogglePlayPause)
                },
                onNavigationItemClick = { destination ->
                    viewModel.handleIntent(MainIntent.NavigateTo(destination))
                }
            )
        }
    ) { paddingValues ->
        // 主要内容区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 导航主机
            AppNavigation(
                navController = navController,
                onSongClick = { song ->
                    viewModel.handleIntent(MainIntent.PlaySong(song))
                },
                onNavigateToSettings = {
                    viewModel.handleIntent(MainIntent.NavigateTo(NavigationDestination.Settings))
                },
                onNavigateBack = {
                    viewModel.handleIntent(MainIntent.NavigateBack)
                }
            )
            
            // 错误提示
            state.errorMessage?.let { errorMessage ->
                LaunchedEffect(errorMessage) {
                    // 可以在这里显示 SnackBar 或其他错误提示
                    // 3秒后自动清除错误
                    kotlinx.coroutines.delay(3000)
                    viewModel.handleIntent(MainIntent.ClearError)
                }
            }
            
            // 加载指示器
            if (state.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
    }
    
    // 初始化示例数据（仅在首次启动时）
    LaunchedEffect(Unit) {
        viewModel.initializeSampleData()
    }
}

/**
 * 响应式主屏幕（适配不同屏幕尺寸）
 */
@Composable
fun ResponsiveMainScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp
    
    when {
        // 平板布局（宽度 > 600dp）
        screenWidth > 600 -> {
            TabletMainScreen(
                viewModel = viewModel,
                modifier = modifier
            )
        }
        // 手机布局
        else -> {
            MainScreen(
                viewModel = viewModel,
                modifier = modifier
            )
        }
    }
}

/**
 * 平板专用主屏幕布局
 */
@Composable
private fun TabletMainScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val state by viewModel.state.collectAsStateWithLifecycle()
    val navController = rememberNavController()
    
    // 平板布局：侧边导航 + 主内容区域
    Row(
        modifier = modifier.fillMaxSize()
    ) {
        // 左侧导航栏（平板模式）
        if (state.shouldShowBottomNavigation) {
            NavigationRail(
                modifier = Modifier.fillMaxHeight()
            ) {
                // TODO: 实现平板导航栏
                Text("平板导航")
            }
        }
        
        // 主内容区域
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
        ) {
            // 内容区域
            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                AppNavigation(
                    navController = navController,
                    onSongClick = { song ->
                        viewModel.handleIntent(MainIntent.PlaySong(song))
                    },
                    onNavigateToSettings = {
                        viewModel.handleIntent(MainIntent.NavigateTo(NavigationDestination.Settings))
                    },
                    onNavigateBack = {
                        viewModel.handleIntent(MainIntent.NavigateBack)
                    }
                )
            }
            
            // 底部迷你播放器（平板模式）
            if (state.shouldShowMiniPlayer) {
                com.example.animebottomqq.presentation.components.MiniPlayer(
                    playerState = state.playerState,
                    onPlayPauseClick = {
                        viewModel.handleIntent(MainIntent.TogglePlayPause)
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 预览组件
 */
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    com.example.animebottomqq.ui.theme.AnimeBottomQQTheme {
        val viewModel = MainViewModel()
        MainScreen(viewModel = viewModel)
    }
}

@androidx.compose.ui.tooling.preview.Preview(
    showBackground = true,
    widthDp = 800,
    heightDp = 600,
    name = "Tablet Layout"
)
@Composable
fun TabletMainScreenPreview() {
    com.example.animebottomqq.ui.theme.AnimeBottomQQTheme {
        val viewModel = MainViewModel()
        ResponsiveMainScreen(viewModel = viewModel)
    }
}
