package com.example.animebottomqq.presentation.state

import com.example.animebottomqq.data.models.MiniPlayerState
import com.example.animebottomqq.data.models.PlayerState
import com.example.animebottomqq.data.models.Song

/**
 * 导航目的地枚举
 */
enum class NavigationDestination(val route: String) {
    Home("home"),
    Live("live"),
    Search("search"),
    Community("community"),
    Profile("profile"),
    Settings("settings") // 设置页面（从"我的"页面进入）
}

/**
 * 应用主状态
 */
data class MainState(
    // 导航状态
    val currentDestination: NavigationDestination = NavigationDestination.Home,
    val isNavigationVisible: Boolean = true,
    
    // 播放器状态
    val playerState: PlayerState = PlayerState(),
    
    // 动画状态
    val isAnimating: Boolean = false,
    val animationProgress: Float = 0f,
    
    // UI 状态
    val isLoading: Boolean = false,
    val errorMessage: String? = null
) {
    /**
     * 计算迷你播放器是否应该显示
     */
    val shouldShowMiniPlayer: Boolean
        get() = playerState.currentSong != null && 
                playerState.miniPlayerState != MiniPlayerState.Hidden
    
    /**
     * 计算底部导航栏是否应该显示
     */
    val shouldShowBottomNavigation: Boolean
        get() = currentDestination != NavigationDestination.Settings
    
    /**
     * 计算底部区域是否应该显示（迷你播放器或导航栏）
     */
    val shouldShowBottomSection: Boolean
        get() = shouldShowMiniPlayer || shouldShowBottomNavigation
}
