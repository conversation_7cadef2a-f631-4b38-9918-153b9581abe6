package com.example.animebottomqq.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.animebottomqq.data.models.BottomNavigationItems
import com.example.animebottomqq.data.models.NavigationItem
import com.example.animebottomqq.presentation.state.NavigationDestination
import com.example.animebottomqq.ui.theme.AnimeBottomQQTheme

/**
 * 自定义底部导航栏组件
 */
@Composable
fun CustomBottomNavigation(
    currentDestination: NavigationDestination,
    onNavigationItemClick: (NavigationDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 根据屏幕宽度调整布局
    val horizontalPadding = if (screenWidth > 600.dp) 24.dp else 8.dp
    val itemSpacing = if (screenWidth > 600.dp) 16.dp else 4.dp
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
                .padding(horizontal = horizontalPadding, vertical = 8.dp)
                .selectableGroup(),
            horizontalArrangement = Arrangement.spacedBy(itemSpacing, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BottomNavigationItems.items.forEach { item ->
                val destination = when (item.route) {
                    "home" -> NavigationDestination.Home
                    "live" -> NavigationDestination.Live
                    "search" -> NavigationDestination.Search
                    "community" -> NavigationDestination.Community
                    "profile" -> NavigationDestination.Profile
                    else -> NavigationDestination.Home
                }
                
                BottomNavigationItem(
                    item = item,
                    isSelected = currentDestination == destination,
                    onClick = { onNavigationItemClick(destination) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 底部导航栏单个项目组件
 */
@Composable
private fun BottomNavigationItem(
    item: NavigationItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 根据屏幕宽度调整图标和文字大小
    val iconSize = if (screenWidth > 600.dp) 28.dp else 24.dp
    val fontSize = if (screenWidth > 600.dp) 13.sp else 11.sp
    
    Column(
        modifier = modifier
            .fillMaxHeight()
            .padding(horizontal = 4.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 导航项按钮
        IconButton(
            onClick = onClick,
            modifier = Modifier.size(48.dp)
        ) {
            Icon(
                imageVector = if (isSelected) item.selectedIcon else item.icon,
                contentDescription = item.title,
                tint = if (isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                },
                modifier = Modifier.size(iconSize)
            )
        }
        
        // 导航项标题
        Text(
            text = item.title,
            style = MaterialTheme.typography.labelSmall.copy(
                fontSize = fontSize,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
            ),
            color = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            },
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CustomBottomNavigationPreview() {
    AnimeBottomQQTheme {
        CustomBottomNavigation(
            currentDestination = NavigationDestination.Home,
            onNavigationItemClick = {}
        )
    }
}

@Preview(showBackground = true, name = "Selected Search")
@Composable
fun CustomBottomNavigationSearchSelectedPreview() {
    AnimeBottomQQTheme {
        CustomBottomNavigation(
            currentDestination = NavigationDestination.Search,
            onNavigationItemClick = {}
        )
    }
}

@Preview(showBackground = true, name = "Selected Profile")
@Composable
fun CustomBottomNavigationProfileSelectedPreview() {
    AnimeBottomQQTheme {
        CustomBottomNavigation(
            currentDestination = NavigationDestination.Profile,
            onNavigationItemClick = {}
        )
    }
}
