package com.example.animebottomqq.presentation.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.animebottomqq.ui.theme.AnimeBottomQQTheme

/**
 * 设置页屏幕（对应截图中的"更多"页面）
 */
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 根据屏幕宽度调整布局
    val horizontalPadding = if (screenWidth > 600.dp) 24.dp else 16.dp
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
        contentPadding = PaddingValues(horizontal = horizontalPadding, vertical = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 顶部标题栏
        item {
            TopBar(
                onBackClick = onBackClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 功能图标网格
        item {
            FunctionIconGrid(
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 设置选项列表
        item {
            SettingsOptionsList(
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 会员相关选项
        item {
            MembershipSection(
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 顶部标题栏
 */
@Composable
private fun TopBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onBackClick) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "返回",
                tint = MaterialTheme.colorScheme.onBackground
            )
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "更多",
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            ),
            color = MaterialTheme.colorScheme.onBackground
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        IconButton(onClick = { /* TODO: 实现扫码功能 */ }) {
            Icon(
                imageVector = Icons.Default.QrCode,
                contentDescription = "扫码",
                tint = MaterialTheme.colorScheme.onBackground
            )
        }
    }
}

/**
 * 功能图标网格
 */
@Composable
private fun FunctionIconGrid(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                FunctionIcon(
                    icon = Icons.Default.Person,
                    title = "装扮中心",
                    backgroundColor = Color(0xFF6200EE)
                )
                FunctionIcon(
                    icon = Icons.Default.MusicNote,
                    title = "音乐基因",
                    backgroundColor = Color(0xFF03DAC6)
                )
                FunctionIcon(
                    icon = Icons.Default.Timeline,
                    title = "弹一弹",
                    backgroundColor = Color(0xFFFF6B6B)
                )
                FunctionIcon(
                    icon = Icons.Default.Games,
                    title = "猜歌王者",
                    backgroundColor = Color(0xFF4ECDC4)
                )
            }
        }
    }
}

/**
 * 功能图标组件
 */
@Composable
private fun FunctionIcon(
    icon: ImageVector,
    title: String,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.clickable { /* TODO: 实现功能点击 */ },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(backgroundColor),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

/**
 * 设置选项列表
 */
@Composable
private fun SettingsOptionsList(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column {
            SettingsItem(
                icon = Icons.Default.Settings,
                title = "设置",
                onClick = { /* TODO: 打开设置 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            SettingsItem(
                icon = Icons.Default.Schedule,
                title = "定时关闭",
                hasSwitch = true,
                onClick = { /* TODO: 定时关闭设置 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            SettingsItem(
                icon = Icons.Default.MusicNote,
                title = "听歌识曲",
                onClick = { /* TODO: 听歌识曲 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            SettingsItem(
                icon = Icons.Default.CleaningServices,
                title = "清理占用空间",
                onClick = { /* TODO: 清理空间 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            SettingsItem(
                icon = Icons.Default.Palette,
                title = "模式与自定义",
                subtitle = "默认模式支持自定义啦",
                onClick = { /* TODO: 模式设置 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            SettingsItem(
                icon = Icons.Default.ChildCare,
                title = "未成年人模式",
                subtitle = "未开启",
                onClick = { /* TODO: 未成年人模式 */ }
            )
        }
    }
}

/**
 * 会员相关部分
 */
@Composable
private fun MembershipSection(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column {
            MembershipItem(
                icon = Icons.Default.PhoneAndroid,
                title = "移动用户免流送会员",
                subtitle = "抽奖领2年绿钻",
                hasRedDot = true,
                onClick = { /* TODO: 移动用户活动 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            MembershipItem(
                icon = Icons.Default.Star,
                title = "免流量听歌送会员",
                subtitle = "送24个月超级会员",
                hasRedDot = true,
                onClick = { /* TODO: 免流量活动 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            MembershipItem(
                icon = Icons.Default.VideoLibrary,
                title = "腾讯视频VIP福利",
                subtitle = "QQ音乐用户专享特惠",
                onClick = { /* TODO: 腾讯视频福利 */ }
            )
            
            Divider(color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f))
            
            MembershipItem(
                icon = Icons.Default.Mic,
                title = "铃声专区",
                subtitle = "铃声全新升级",
                hasRedDot = true,
                onClick = { /* TODO: 铃声专区 */ }
            )
        }
    }
}

/**
 * 设置项目组件
 */
@Composable
private fun SettingsItem(
    icon: ImageVector,
    title: String,
    subtitle: String? = null,
    hasSwitch: Boolean = false,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var switchState by remember { mutableStateOf(false) }
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            
            if (subtitle != null) {
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
        
        if (hasSwitch) {
            Switch(
                checked = switchState,
                onCheckedChange = { switchState = it }
            )
        } else {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "进入",
                tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

/**
 * 会员项目组件
 */
@Composable
private fun MembershipItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    hasRedDot: Boolean = false,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            
            if (hasRedDot) {
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .clip(CircleShape)
                        .background(Color.Red)
                        .align(Alignment.TopEnd)
                )
            }
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }
        
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "进入",
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f),
            modifier = Modifier.size(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    AnimeBottomQQTheme {
        SettingsScreen()
    }
}
