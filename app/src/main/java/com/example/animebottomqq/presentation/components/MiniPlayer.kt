package com.example.animebottomqq.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.animebottomqq.data.models.PlaybackState
import com.example.animebottomqq.data.models.PlayerState
import com.example.animebottomqq.data.models.Song
import com.example.animebottomqq.ui.theme.AnimeBottomQQTheme

/**
 * 迷你播放器组件
 */
@Composable
fun MiniPlayer(
    playerState: PlayerState,
    onPlayPauseClick: () -> Unit,
    onPlayerClick: () -> Unit = {},
    onMoreClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    
    // 根据屏幕宽度调整布局
    val horizontalPadding = if (screenWidth > 600.dp) 24.dp else 16.dp
    val albumCoverSize = if (screenWidth > 600.dp) 56.dp else 48.dp
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(72.dp)
            .clickable { onPlayerClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = horizontalPadding, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 专辑封面
            AlbumCover(
                albumCover = playerState.currentSong?.albumCover,
                size = albumCoverSize,
                modifier = Modifier.padding(end = 12.dp)
            )
            
            // 歌曲信息
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 8.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = playerState.currentSong?.title ?: "未知歌曲",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp
                    ),
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(2.dp))
                
                Text(
                    text = playerState.currentSong?.artist ?: "未知艺术家",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 12.sp
                    ),
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 播放/暂停按钮
            IconButton(
                onClick = onPlayPauseClick,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = when (playerState.playbackState) {
                        PlaybackState.Playing -> Icons.Default.Pause
                        else -> Icons.Default.PlayArrow
                    },
                    contentDescription = when (playerState.playbackState) {
                        PlaybackState.Playing -> "暂停"
                        else -> "播放"
                    },
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // 更多选项按钮
            IconButton(
                onClick = onMoreClick,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.MoreVert,
                    contentDescription = "更多选项",
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 专辑封面组件
 */
@Composable
private fun AlbumCover(
    albumCover: String?,
    size: androidx.compose.ui.unit.Dp,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(RoundedCornerShape(8.dp))
            .background(
                color = MaterialTheme.colorScheme.surfaceVariant
            ),
        contentAlignment = Alignment.Center
    ) {
        // 如果有专辑封面URL，这里可以使用 AsyncImage 加载
        // 现在使用占位符
        if (albumCover != null) {
            // TODO: 使用 Coil 或其他图片加载库加载专辑封面
            // AsyncImage(...)
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "专辑封面",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                modifier = Modifier.size(size * 0.4f)
            )
        } else {
            // 默认占位符
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "默认专辑封面",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                modifier = Modifier.size(size * 0.4f)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MiniPlayerPreview() {
    AnimeBottomQQTheme {
        val samplePlayerState = PlayerState(
            currentSong = Song(
                id = "1",
                title = "把回忆拼好给你 (Live)",
                artist = "Miu",
                albumCover = null
            ),
            playbackState = PlaybackState.Playing
        )
        
        MiniPlayer(
            playerState = samplePlayerState,
            onPlayPauseClick = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(showBackground = true, name = "Paused State")
@Composable
fun MiniPlayerPausedPreview() {
    AnimeBottomQQTheme {
        val samplePlayerState = PlayerState(
            currentSong = Song(
                id = "1",
                title = "很长的歌曲名称用来测试文本溢出效果",
                artist = "很长的艺术家名称用来测试",
                albumCover = null
            ),
            playbackState = PlaybackState.Paused
        )
        
        MiniPlayer(
            playerState = samplePlayerState,
            onPlayPauseClick = {},
            modifier = Modifier.padding(16.dp)
        )
    }
}
