package com.example.animebottomqq.presentation.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.animebottomqq.data.models.PlayerState
import com.example.animebottomqq.presentation.state.MainState
import com.example.animebottomqq.presentation.state.NavigationDestination

/**
 * 动画底部区域组件
 * 包含迷你播放器和底部导航栏，支持同步动画效果
 */
@Composable
fun AnimatedBottomSection(
    state: MainState,
    onPlayPauseClick: () -> Unit,
    onNavigationItemClick: (NavigationDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    // 计算底部区域的总高度
    val miniPlayerHeight = 72.dp
    val bottomNavigationHeight = 80.dp
    val totalHeight = miniPlayerHeight + bottomNavigationHeight
    
    // 动画状态
    val animationProgress by animateFloatAsState(
        targetValue = if (state.shouldShowBottomSection) 1f else 0f,
        animationSpec = tween(
            durationMillis = 300,
            easing = FastOutSlowInEasing
        ),
        label = "bottomSectionAnimation"
    )
    
    // 根据当前页面和播放器状态决定显示内容
    val showMiniPlayer = state.shouldShowMiniPlayer
    val showBottomNavigation = state.shouldShowBottomNavigation
    
    // 计算偏移量
    val offsetY = with(density) {
        when {
            // 设置页：只显示迷你播放器，导航栏隐藏
            state.currentDestination == NavigationDestination.Settings -> {
                if (showMiniPlayer) {
                    // 迷你播放器正常显示，导航栏向下偏移隐藏
                    bottomNavigationHeight.toPx() * (1f - animationProgress)
                } else {
                    // 迷你播放器也隐藏
                    totalHeight.toPx() * (1f - animationProgress)
                }
            }
            // 其他页面：显示导航栏，迷你播放器根据播放状态显示
            else -> {
                if (showMiniPlayer) {
                    // 都显示，无偏移
                    0f
                } else {
                    // 只显示导航栏，迷你播放器向下偏移隐藏
                    miniPlayerHeight.toPx() * (1f - animationProgress)
                }
            }
        }
    }
    
    // 动画容器
    AnimatedVisibility(
        visible = state.shouldShowBottomSection,
        enter = slideInVertically(
            initialOffsetY = { it },
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ) + fadeIn(
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ),
        exit = slideOutVertically(
            targetOffsetY = { it },
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ) + fadeOut(
            animationSpec = tween(300, easing = FastOutSlowInEasing)
        ),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.offset(y = with(density) { offsetY.toDp() })
        ) {
            // 迷你播放器
            AnimatedVisibility(
                visible = showMiniPlayer,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeIn(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeOut(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                )
            ) {
                MiniPlayer(
                    playerState = state.playerState,
                    onPlayPauseClick = onPlayPauseClick,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // 底部导航栏
            AnimatedVisibility(
                visible = showBottomNavigation,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeIn(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                ) + fadeOut(
                    animationSpec = tween(300, easing = FastOutSlowInEasing)
                )
            ) {
                CustomBottomNavigation(
                    currentDestination = state.currentDestination,
                    onNavigationItemClick = onNavigationItemClick,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 简化版动画底部区域（用于特殊场景）
 */
@Composable
fun SimpleAnimatedBottomSection(
    showMiniPlayer: Boolean,
    showBottomNavigation: Boolean,
    playerState: PlayerState,
    currentDestination: NavigationDestination,
    onPlayPauseClick: () -> Unit,
    onNavigationItemClick: (NavigationDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 迷你播放器动画
        AnimatedVisibility(
            visible = showMiniPlayer,
            enter = slideInVertically(
                initialOffsetY = { it },
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ) + fadeIn(
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ),
            exit = slideOutVertically(
                targetOffsetY = { it },
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ) + fadeOut(
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            )
        ) {
            MiniPlayer(
                playerState = playerState,
                onPlayPauseClick = onPlayPauseClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // 底部导航栏动画
        AnimatedVisibility(
            visible = showBottomNavigation,
            enter = slideInVertically(
                initialOffsetY = { it },
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ) + fadeIn(
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ),
            exit = slideOutVertically(
                targetOffsetY = { it },
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            ) + fadeOut(
                animationSpec = tween(300, easing = FastOutSlowInEasing)
            )
        ) {
            CustomBottomNavigation(
                currentDestination = currentDestination,
                onNavigationItemClick = onNavigationItemClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}
