package com.example.animebottomqq.presentation.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.animebottomqq.data.models.PlayerState
import com.example.animebottomqq.presentation.state.MainState
import com.example.animebottomqq.presentation.state.NavigationDestination

/**
 * 动画底部区域组件
 * 迷你播放器和底部导航栏作为一个整体进行位移动画
 */
@Composable
fun AnimatedBottomSection(
    state: MainState,
    onPlayPauseClick: () -> Unit,
    onNavigationItemClick: (NavigationDestination) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current

    // 计算组件高度
    val miniPlayerHeight = 72.dp
    val bottomNavigationHeight = 80.dp

    // 根据当前页面和播放器状态决定显示内容
    val showMiniPlayer = state.shouldShowMiniPlayer
    val showBottomNavigation = state.shouldShowBottomNavigation

    // 计算目标偏移量（关键改进：整体位移逻辑）
    val targetOffsetY = with(density) {
        when {
            // 设置页：只显示迷你播放器，导航栏向下隐藏
            state.currentDestination == NavigationDestination.Settings -> {
                if (showMiniPlayer) {
                    bottomNavigationHeight.toPx() // 导航栏高度的偏移，隐藏导航栏
                } else {
                    (miniPlayerHeight + bottomNavigationHeight).toPx() // 全部隐藏
                }
            }
            // 其他页面：显示导航栏
            else -> {
                if (showMiniPlayer) {
                    0f // 全部显示，无偏移
                } else {
                    miniPlayerHeight.toPx() // 隐藏迷你播放器
                }
            }
        }
    }

    // 使用 Spring 动画提供更自然的缓动效果
    val animatedOffsetY by animateFloatAsState(
        targetValue = targetOffsetY,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "bottomSectionOffset"
    )

    // 整体容器 - 关键改进：作为一个整体进行位移
    Column(
        modifier = modifier.offset(y = with(density) { animatedOffsetY.toDp() })
    ) {
        // 迷你播放器 - 始终存在，通过整体偏移控制显示
        if (showMiniPlayer) {
            MiniPlayer(
                playerState = state.playerState,
                onPlayPauseClick = onPlayPauseClick,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // 底部导航栏 - 始终存在，通过整体偏移控制显示
        if (showBottomNavigation) {
            CustomBottomNavigation(
                currentDestination = state.currentDestination,
                onNavigationItemClick = onNavigationItemClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}


