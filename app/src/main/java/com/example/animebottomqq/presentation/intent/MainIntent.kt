package com.example.animebottomqq.presentation.intent

import com.example.animebottomqq.data.models.Song
import com.example.animebottomqq.presentation.state.NavigationDestination

/**
 * 主界面用户意图
 */
sealed class MainIntent {
    
    // 导航相关意图
    data class NavigateTo(val destination: NavigationDestination) : MainIntent()
    object NavigateBack : MainIntent()
    
    // 播放器控制意图
    object TogglePlayPause : MainIntent()
    data class PlaySong(val song: Song) : MainIntent()
    object StopPlayback : MainIntent()
    data class SeekTo(val position: Long) : MainIntent()
    
    // 迷你播放器显示控制意图
    object ShowMiniPlayer : MainIntent()
    object HideMiniPlayer : MainIntent()
    
    // 动画控制意图
    object StartAnimation : MainIntent()
    object EndAnimation : MainIntent()
    data class UpdateAnimationProgress(val progress: Float) : MainIntent()
    
    // 错误处理意图
    data class ShowError(val message: String) : MainIntent()
    object ClearError : MainIntent()
    
    // 加载状态意图
    object StartLoading : MainIntent()
    object StopLoading : MainIntent()
}
