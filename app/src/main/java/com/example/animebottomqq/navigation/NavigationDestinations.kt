package com.example.animebottomqq.navigation

/**
 * 导航目的地常量定义
 */
object NavigationDestinations {
    const val HOME = "home"
    const val LIVE = "live"
    const val SEARCH = "search"
    const val COMMUNITY = "community"
    const val PROFILE = "profile"
    const val SETTINGS = "settings"
}

/**
 * 导航路由扩展函数
 */
fun String.toNavigationDestination(): com.example.animebottomqq.presentation.state.NavigationDestination {
    return when (this) {
        NavigationDestinations.HOME -> com.example.animebottomqq.presentation.state.NavigationDestination.Home
        NavigationDestinations.LIVE -> com.example.animebottomqq.presentation.state.NavigationDestination.Live
        NavigationDestinations.SEARCH -> com.example.animebottomqq.presentation.state.NavigationDestination.Search
        NavigationDestinations.COMMUNITY -> com.example.animebottomqq.presentation.state.NavigationDestination.Community
        NavigationDestinations.PROFILE -> com.example.animebottomqq.presentation.state.NavigationDestination.Profile
        NavigationDestinations.SETTINGS -> com.example.animebottomqq.presentation.state.NavigationDestination.Settings
        else -> com.example.animebottomqq.presentation.state.NavigationDestination.Home
    }
}

/**
 * 导航目的地转换为路由字符串
 */
fun com.example.animebottomqq.presentation.state.NavigationDestination.toRoute(): String {
    return when (this) {
        com.example.animebottomqq.presentation.state.NavigationDestination.Home -> NavigationDestinations.HOME
        com.example.animebottomqq.presentation.state.NavigationDestination.Live -> NavigationDestinations.LIVE
        com.example.animebottomqq.presentation.state.NavigationDestination.Search -> NavigationDestinations.SEARCH
        com.example.animebottomqq.presentation.state.NavigationDestination.Community -> NavigationDestinations.COMMUNITY
        com.example.animebottomqq.presentation.state.NavigationDestination.Profile -> NavigationDestinations.PROFILE
        com.example.animebottomqq.presentation.state.NavigationDestination.Settings -> NavigationDestinations.SETTINGS
    }
}
