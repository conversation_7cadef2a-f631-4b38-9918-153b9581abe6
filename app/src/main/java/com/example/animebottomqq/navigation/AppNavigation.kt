package com.example.animebottomqq.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.animebottomqq.data.models.Song
import com.example.animebottomqq.presentation.screens.HomeScreen
import com.example.animebottomqq.presentation.screens.SettingsScreen

/**
 * 应用导航配置
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    onSongClick: (Song) -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = NavigationDestinations.HOME,
        modifier = modifier.fillMaxSize(),
        enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
        exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
    ) {
        // 主页
        composable(
            route = NavigationDestinations.HOME,
            enterTransition = {
                when (initialState.destination.route) {
                    NavigationDestinations.SETTINGS -> {
                        // 从设置页返回主页的淡入动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        // 其他情况的默认动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            },
            exitTransition = {
                when (targetState.destination.route) {
                    NavigationDestinations.SETTINGS -> {
                        // 导航到设置页的淡出动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        // 其他情况的默认动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            }
        ) {
            HomeScreen(
                onSongClick = onSongClick,
                onSearchClick = {
                    navController.navigate(NavigationDestinations.SEARCH)
                }
            )
        }
        
        // 直播页
        composable(
            route = NavigationDestinations.LIVE,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "直播")
        }
        
        // 搜索页
        composable(
            route = NavigationDestinations.SEARCH,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "搜索")
        }
        
        // 社区页
        composable(
            route = NavigationDestinations.COMMUNITY,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "社区")
        }
        
        // 我的页面
        composable(
            route = NavigationDestinations.PROFILE,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            ProfileScreen(
                onNavigateToSettings = onNavigateToSettings
            )
        }
        
        // 设置页
        composable(
            route = NavigationDestinations.SETTINGS,
            enterTransition = {
                when (initialState.destination.route) {
                    NavigationDestinations.PROFILE -> {
                        // 从"我的"页面进入设置页的淡入动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            },
            exitTransition = {
                when (targetState.destination.route) {
                    NavigationDestinations.PROFILE -> {
                        // 从设置页返回"我的"页面的淡出动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            }
        ) {
            SettingsScreen(
                onBackClick = onNavigateBack
            )
        }
    }
}

/**
 * 占位符屏幕（用于未实现的页面）
 */
@Composable
private fun PlaceholderScreen(
    title: String,
    modifier: Modifier = Modifier
) {
    androidx.compose.foundation.layout.Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        androidx.compose.material3.Text(
            text = "${title}页面",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium,
            color = androidx.compose.material3.MaterialTheme.colorScheme.onBackground
        )
    }
}

/**
 * "我的"页面（简化版，主要用于导航到设置页）
 */
@Composable
private fun ProfileScreen(
    onNavigateToSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    androidx.compose.foundation.layout.Column(
        modifier = modifier
            .fillMaxSize()
            .androidx.compose.foundation.layout.padding(16.dp),
        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
        verticalArrangement = androidx.compose.foundation.layout.Arrangement.Center
    ) {
        androidx.compose.material3.Text(
            text = "我的",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium,
            color = androidx.compose.material3.MaterialTheme.colorScheme.onBackground
        )
        
        androidx.compose.foundation.layout.Spacer(modifier = androidx.compose.ui.Modifier.height(32.dp))
        
        androidx.compose.material3.Button(
            onClick = onNavigateToSettings
        ) {
            androidx.compose.material3.Text("进入设置页")
        }
        
        androidx.compose.foundation.layout.Spacer(modifier = androidx.compose.ui.Modifier.height(16.dp))
        
        androidx.compose.material3.Text(
            text = "点击上方按钮可以看到迷你播放器和导航栏的动画效果",
            style = androidx.compose.material3.MaterialTheme.typography.bodyMedium,
            color = androidx.compose.material3.MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
            textAlign = androidx.compose.ui.text.style.TextAlign.Center
        )
    }
}
