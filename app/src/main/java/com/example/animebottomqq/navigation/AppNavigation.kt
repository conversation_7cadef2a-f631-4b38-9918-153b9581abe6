package com.example.animebottomqq.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.animebottomqq.data.models.Song
import com.example.animebottomqq.presentation.screens.HomeScreen
import com.example.animebottomqq.presentation.screens.SettingsScreen

/**
 * 应用导航配置
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    onSongClick: (Song) -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = NavigationDestinations.HOME,
        modifier = modifier.fillMaxSize(),
        enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
        exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
    ) {
        // 主页
        composable(
            route = NavigationDestinations.HOME,
            enterTransition = {
                when (initialState.destination.route) {
                    NavigationDestinations.SETTINGS -> {
                        // 从设置页返回主页的淡入动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        // 其他情况的默认动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            },
            exitTransition = {
                when (targetState.destination.route) {
                    NavigationDestinations.SETTINGS -> {
                        // 导航到设置页的淡出动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        // 其他情况的默认动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            }
        ) {
            HomeScreen(
                onSongClick = onSongClick,
                onSearchClick = {
                    navController.navigate(NavigationDestinations.SEARCH)
                }
            )
        }
        
        // 直播页
        composable(
            route = NavigationDestinations.LIVE,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "直播")
        }
        
        // 搜索页
        composable(
            route = NavigationDestinations.SEARCH,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "搜索")
        }
        
        // 社区页
        composable(
            route = NavigationDestinations.COMMUNITY,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            PlaceholderScreen(title = "社区")
        }
        
        // 我的页面
        composable(
            route = NavigationDestinations.PROFILE,
            enterTransition = { fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing)) },
            exitTransition = { fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing)) }
        ) {
            ProfileScreen(
                onNavigateToSettings = onNavigateToSettings
            )
        }
        
        // 设置页
        composable(
            route = NavigationDestinations.SETTINGS,
            enterTransition = {
                when (initialState.destination.route) {
                    NavigationDestinations.PROFILE -> {
                        // 从"我的"页面进入设置页的淡入动画
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        fadeIn(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            },
            exitTransition = {
                when (targetState.destination.route) {
                    NavigationDestinations.PROFILE -> {
                        // 从设置页返回"我的"页面的淡出动画
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                    else -> {
                        fadeOut(animationSpec = tween(300, easing = FastOutSlowInEasing))
                    }
                }
            }
        ) {
            SettingsScreen(
                onBackClick = onNavigateBack
            )
        }
    }
}

/**
 * 占位符屏幕（用于未实现的页面）
 */
@Composable
private fun PlaceholderScreen(
    title: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "${title}页面",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onBackground
        )
    }
}

/**
 * "我的"页面（简化版，主要用于导航到设置页）
 */
@Composable
private fun ProfileScreen(
    onNavigateToSettings: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "我的",
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onBackground
        )

        Spacer(modifier = Modifier.height(32.dp))

        Button(
            onClick = onNavigateToSettings
        ) {
            Text("进入设置页")
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "点击上方按钮可以看到迷你播放器和导航栏的动画效果",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}
