package com.example.animebottomqq

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.animebottomqq.presentation.screens.ResponsiveMainScreen
import com.example.animebottomqq.presentation.viewmodel.MainViewModel
import com.example.animebottomqq.ui.theme.AnimeBottomQQTheme

/**
 * 主活动 - 音乐播放器应用入口
 */
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            AnimeBottomQQTheme {
                MusicApp()
            }
        }
    }
}

/**
 * 音乐应用主组件
 */
@Composable
fun MusicApp(
    modifier: Modifier = Modifier
) {
    val viewModel: MainViewModel = viewModel()

    ResponsiveMainScreen(
        viewModel = viewModel,
        modifier = modifier.fillMaxSize()
    )
}

@Preview(showBackground = true)
@Composable
fun MusicAppPreview() {
    AnimeBottomQQTheme {
        MusicApp()
    }
}