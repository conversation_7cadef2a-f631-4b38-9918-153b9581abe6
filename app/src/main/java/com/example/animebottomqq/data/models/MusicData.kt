package com.example.animebottomqq.data.models

/**
 * 歌曲数据模型
 */
data class Song(
    val id: String,
    val title: String,
    val artist: String,
    val albumCover: String? = null, // 专辑封面 URL 或资源路径
    val duration: Long = 0L, // 歌曲时长（毫秒）
    val isPlaying: Boolean = false
)

/**
 * 迷你播放器状态枚举
 */
enum class MiniPlayerState {
    Hidden,    // 隐藏状态
    Visible,   // 显示状态
    Animating  // 动画中状态
}

/**
 * 播放状态枚举
 */
enum class PlaybackState {
    Playing,   // 播放中
    Paused,    // 暂停
    Stopped    // 停止
}

/**
 * 播放器数据状态
 */
data class PlayerState(
    val currentSong: Song? = null,
    val playbackState: PlaybackState = PlaybackState.Stopped,
    val currentPosition: Long = 0L, // 当前播放位置（毫秒）
    val miniPlayerState: MiniPlayerState = MiniPlayerState.Hidden
)
