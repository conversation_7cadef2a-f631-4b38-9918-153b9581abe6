package com.example.animebottomqq.data.models

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 导航项数据模型
 */
data class NavigationItem(
    val route: String,
    val title: String,
    val icon: ImageVector,
    val selectedIcon: ImageVector = icon
)

/**
 * 底部导航栏项目定义
 */
object BottomNavigationItems {
    val items = listOf(
        NavigationItem(
            route = "home",
            title = "首页",
            icon = Icons.Default.Home,
            selectedIcon = Icons.Default.Home
        ),
        NavigationItem(
            route = "live",
            title = "直播",
            icon = Icons.Default.PlayArrow,
            selectedIcon = Icons.Default.PlayArrow
        ),
        NavigationItem(
            route = "search",
            title = "搜索",
            icon = Icons.Default.Search,
            selectedIcon = Icons.Default.Search
        ),
        NavigationItem(
            route = "community",
            title = "社区",
            icon = Icons.Default.Group,
            selectedIcon = Icons.Default.Group
        ),
        NavigationItem(
            route = "profile",
            title = "我的",
            icon = Icons.Default.Person,
            selectedIcon = Icons.Default.Person
        )
    )
}
